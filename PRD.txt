I need to develop a cross-platform proxy tool UI using <PERSON>ust with <PERSON><PERSON>. The proxy will use a custom protocol (which I'll implement separately), but you need to design trait interfaces for easy integration with my existing Rust crate that handles TCP and native UDP proxying.

## Core Requirements:

### 1. Proxy Mode Support
Implement three proxy modes with a simple UI:
- **Global Mode**: All traffic goes through proxy
- **Rule Mode**: Traffic routing based on built-in rules  
- **Direct Mode**: No proxy, direct connections

**UI Constraint**: Users can only select a proxy node from a dropdown (populated via trait interface), not configure rules. Rules come from config file only.

### 2. TUN/TAP Traffic Interception
- **Required Dependency**: Must use `tun-easytier` crate (https://crates.io/crates/tun-easytier) for TUN/TAP functionality
- **Traffic Loop Prevention**: Implement logic to prevent traffic loops
- **Cross-platform Compatibility**: Handle platform-specific routing table modifications
- **System Route Management**: Implement cross-platform system route table updates

### 3. FakeIP DNS Strategy
Implement FakeIP strategy to:
- Capture DNS domain names being accessed
- Route DNS requests through proxy
- Maintain IP-to-domain mapping for rule matching

### 4. Encrypted Config Auto-Download
- **Source**: Download from hardcoded URL
- **Encryption**: AES-128-GCM encrypted config files
- **Key Management**: Encryption key hardcoded in application
- **Security**: Never save config to disk - download on each startup
- **Config Structure**: 
  - Proxy nodes (string format compatible with my backend protocol)
  - Proxy rules

### 5. Proxy Rule Engine
Support these rule types with format: `TYPE,VALUE,NODE_ID`
- `DOMAIN-SUFFIX,google.com,1`
- `DOMAIN,google.com,1` 
- `DOMAIN-KEYWORD,google,1`
- `IP-CIDR,*******/32,1`
- `GEOIP,US,1`
- `GEOSITE,google,1`

**Node Selection**: Use first node from trait interface as default fallback for rule mode and global mode.

### 6. Packet Processing & State Management
- Parse TUN/TAP packets (TCP/UDP)
- Maintain TCP connection state (track SYN packets)
- Extract destination IPs and DNS requests
- Interface with backend for TCP stream and UDP packet proxying
- **Important**: Backend only handles TCP streams and UDP requests - all packet parsing/reconstruction is your responsibility

### 7. Admin Privileges
Use `privilege` crate (https://docs.rs/privilege/0.3.0/privilege/) for elevation handling across platforms.

### 8. Testing with SOCKS5
For functionality verification, implement SOCKS5 protocol support using test server:
- **Server**: **************:8888
- **Auth**: username="lunyduo", password="lunyduo888"

### 9. Project Setup
Use Tauri CLI scaffolding and follow Tauri best practices for project structure and development workflow.

## UI/UX Specifications:

### Overall Design
- **Window**: Fixed-size, non-resizable main window
- **Theme**: Modern dark theme with clean typography
- **Layout**: Centered dashboard-style card layout

### Core UI Components:

#### 1. Connection Toggle (Primary Control)
- **Design**: Large circular button with power icon, most prominent UI element
- **States**:
  - Disconnected: Gray background, gray icon, "Click to Connect" text
  - Connecting: Blue background, spinning animation, "Connecting..." text  
  - Connected: Green background, white icon, connection duration display
  - Error: Red background, warning icon, error message

#### 2. Status Display (Above toggle)
- Current connection status text
- Active proxy node name when connected

#### 3. Mode Selector
- **Design**: 3-segment control with "Global", "Rules", "Direct" options
- **Behavior**: Immediate mode switching when connected, no reconnection required
- **State**: Disable node selector in Direct mode

#### 4. Node Selector  
- **Design**: Dropdown populated by `CoreProxy::get_node_names()`
- **Function**: Sets global mode node and rule mode fallback
- **State**: Disabled in Direct mode

#### 5. Real-time Speed Chart
- **Design**: 60-second line chart showing upload/download speeds
- **Features**: Auto-scaling Y-axis, current speed display in corner
- **Colors**: Different colors for upload (↑) and download (↓)

#### 6. Log Viewer (Collapsible)
- **Trigger**: "Log" icon button in bottom-right corner
- **Design**: Slides up from bottom, occupies ~1/3 window height
- **Features**: 
  - Real-time log streaming
  - Color-coded log levels (INFO/WARN/ERROR)
  - Copy and Clear buttons
  - Auto-scroll to latest entries

## Development Environment:
- **Linux Build**: Use `cargo zigbuild --release --target x86_64-unknown-linux-gnu`
- **Test Environment**: Linux VM at 211.99.101.78:30026 (SSH key configured)
- **Reference Code**: EasyTier project at `/Users/<USER>/code/github/EasyTier/` for VPN/proxy implementation patterns

## Required Trait Interfaces:
Design clean trait interfaces for:
- Proxy node management (`get_node_names()`, node selection)
- Backend protocol integration (TCP stream, UDP packet handling)
- Configuration management (encrypted config parsing)
- Traffic routing decisions (rule matching, mode switching)