{"$schema": "https://schema.tauri.app/config/2", "productName": "TunProxyUI", "version": "0.1.0", "identifier": "com.tunproxy.ui", "build": {"frontendDist": "../dist", "devUrl": "http://localhost:1420", "beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build"}, "app": {"windows": [{"title": "Tun Proxy", "width": 400, "height": 700, "resizable": false, "fullscreen": false, "center": true, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}