use std::sync::Arc;
use tokio::sync::RwLock;
use log::{info, error};
use tauri::Manager;

// Module declarations
mod traits;
mod tun;
mod packet;
mod dns;
mod rules;
mod config;
mod proxy;
mod socks5;
mod route;

use traits::*;
use tun::TunManager;
use dns::FakeIpDnsManager;
use rules::RuleBasedTrafficRouter;
use config::EncryptedConfigManager;
use proxy::ProxyManager;

/// Main application state
pub struct AppState {
    proxy_manager: Arc<ProxyManager>,
    tun_manager: Arc<RwLock<TunManager>>,
    config_manager: Arc<EncryptedConfigManager>,
    dns_manager: Arc<FakeIpDnsManager>,
    traffic_router: Arc<RuleBasedTrafficRouter>,
}

impl AppState {
    pub async fn new() -> anyhow::Result<Self> {
        // Initialize managers
        let config_manager = Arc::new(EncryptedConfigManager::new());
        let dns_manager = Arc::new(FakeIpDnsManager::new());
        let traffic_router = Arc::new(RuleBasedTrafficRouter::new());

        // Create proxy manager
        let proxy_manager = Arc::new(ProxyManager::new(
            Arc::clone(&config_manager) as Arc<dyn ConfigManager>,
            Arc::clone(&traffic_router) as Arc<dyn TrafficRouter>,
        ).await?);

        // Create TUN manager
        let tun_manager = Arc::new(RwLock::new(TunManager::new(
            Arc::clone(&dns_manager) as Arc<dyn DnsManager>,
            Arc::clone(&traffic_router) as Arc<dyn TrafficRouter>,
            Arc::clone(&proxy_manager) as Arc<dyn BackendProxy>,
        )));

        Ok(Self {
            proxy_manager,
            tun_manager,
            config_manager,
            dns_manager,
            traffic_router,
        })
    }
}

// Tauri commands
#[tauri::command]
async fn get_proxy_nodes(state: tauri::State<'_, AppState>) -> Result<Vec<String>, String> {
    state.proxy_manager.get_node_names().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn set_proxy_mode(mode: String, state: tauri::State<'_, AppState>) -> Result<(), String> {
    let proxy_mode = match mode.as_str() {
        "global" => ProxyMode::Global,
        "rules" => ProxyMode::Rules,
        "direct" => ProxyMode::Direct,
        _ => return Err("Invalid proxy mode".to_string()),
    };

    state.proxy_manager.set_proxy_mode(proxy_mode).await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn connect_proxy(state: tauri::State<'_, AppState>) -> Result<(), String> {
    // Start TUN interface
    {
        let mut tun = state.tun_manager.write().await;
        tun.start().await.map_err(|e| e.to_string())?;
    }

    // Connect proxy
    state.proxy_manager.connect().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn disconnect_proxy(state: tauri::State<'_, AppState>) -> Result<(), String> {
    // Disconnect proxy
    state.proxy_manager.disconnect().await
        .map_err(|e| e.to_string())?;

    // Stop TUN interface
    {
        let mut tun = state.tun_manager.write().await;
        tun.stop().await.map_err(|e| e.to_string())?;
    }

    Ok(())
}

#[tauri::command]
async fn get_connection_state(state: tauri::State<'_, AppState>) -> Result<ConnectionState, String> {
    state.proxy_manager.get_connection_state().await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_traffic_stats(state: tauri::State<'_, AppState>) -> Result<TrafficStats, String> {
    state.proxy_manager.get_traffic_stats().await
        .map_err(|e| e.to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .setup(|app| {
            // Setup logging
            if cfg!(debug_assertions) {
                app.handle().plugin(
                    tauri_plugin_log::Builder::default()
                        .level(log::LevelFilter::Debug)
                        .build(),
                )?;
            }

            // Initialize app state
            let handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                match AppState::new().await {
                    Ok(state) => {
                        handle.manage(state);
                        info!("Application initialized successfully");
                    }
                    Err(e) => {
                        error!("Failed to initialize application: {}", e);
                    }
                }
            });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_proxy_nodes,
            set_proxy_mode,
            connect_proxy,
            disconnect_proxy,
            get_connection_state,
            get_traffic_stats
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
