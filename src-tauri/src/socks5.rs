use anyhow::{Result, anyhow};
use std::net::SocketAddr;
use tokio::net::TcpStream;
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use tokio_socks::tcp::Socks5Stream;
use log::{debug, info, warn, error};

/// SOCKS5 client for proxy connections
pub struct Socks5Client {
    proxy_addr: String,
    username: Option<String>,
    password: Option<String>,
}

impl Socks5Client {
    /// Create new SOCKS5 client and test connection
    pub async fn new(
        proxy_addr: &str,
        username: Option<String>,
        password: Option<String>,
    ) -> Result<Self> {
        let client = Self {
            proxy_addr: proxy_addr.to_string(),
            username,
            password,
        };

        // Test connection
        client.test_connection().await?;

        Ok(client)
    }

    /// Test SOCKS5 proxy connection
    async fn test_connection(&self) -> Result<()> {
        info!("Testing SOCKS5 connection to {}", self.proxy_addr);

        let target_addr = "httpbin.org:80";
        
        match self.create_socks5_connection(target_addr).await {
            Ok(_stream) => {
                info!("SOCKS5 proxy connection test successful");
                Ok(())
            }
            Err(e) => {
                error!("SOCKS5 proxy connection test failed: {}", e);
                Err(e)
            }
        }
    }

    /// Create SOCKS5 connection to target
    async fn create_socks5_connection(&self, target_addr: &str) -> Result<Socks5Stream<TcpStream>> {
        let proxy_addr = self.proxy_addr.parse::<SocketAddr>()
            .map_err(|e| anyhow!("Invalid proxy address {}: {}", self.proxy_addr, e))?;

        let stream = if let (Some(username), Some(password)) = (&self.username, &self.password) {
            // Authenticated connection
            debug!("Connecting to SOCKS5 proxy with authentication: {}", self.proxy_addr);
            Socks5Stream::connect_with_password(
                proxy_addr,
                target_addr,
                username,
                password,
            ).await
        } else {
            // Anonymous connection
            debug!("Connecting to SOCKS5 proxy without authentication: {}", self.proxy_addr);
            Socks5Stream::connect(proxy_addr, target_addr).await
        };

        stream.map_err(|e| anyhow!("SOCKS5 connection failed: {}", e))
    }

    /// Handle TCP data through SOCKS5 proxy
    pub async fn handle_tcp_data(&self, target_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        debug!("Handling TCP data for {} ({} bytes)", target_addr, data.len());

        let target_str = target_addr.to_string();
        let mut stream = self.create_socks5_connection(&target_str).await?;

        // Send data
        stream.write_all(data).await
            .map_err(|e| anyhow!("Failed to send data through SOCKS5: {}", e))?;

        // Read response
        let mut response = Vec::new();
        let mut buffer = [0u8; 4096];

        // Set a timeout for reading
        let read_result = tokio::time::timeout(
            std::time::Duration::from_secs(10),
            stream.read(&mut buffer)
        ).await;

        match read_result {
            Ok(Ok(0)) => {
                debug!("Connection closed by remote");
            }
            Ok(Ok(n)) => {
                response.extend_from_slice(&buffer[..n]);
                debug!("Received {} bytes through SOCKS5", n);
            }
            Ok(Err(e)) => {
                warn!("Error reading from SOCKS5 stream: {}", e);
            }
            Err(_) => {
                debug!("Read timeout on SOCKS5 stream");
            }
        }

        Ok(response)
    }

    /// Handle UDP data through SOCKS5 proxy (simplified implementation)
    pub async fn handle_udp_data(&self, target_addr: SocketAddr, data: &[u8]) -> Result<Vec<u8>> {
        debug!("Handling UDP data for {} ({} bytes)", target_addr, data.len());

        // For UDP, we'll use TCP connection to the target for simplicity
        // In a real implementation, you'd use SOCKS5 UDP associate
        self.handle_tcp_data(target_addr, data).await
    }

    /// Send HTTP request through SOCKS5 proxy (for testing)
    pub async fn send_http_request(&self, host: &str, path: &str) -> Result<String> {
        let target_addr = format!("{}:80", host);
        let mut stream = self.create_socks5_connection(&target_addr).await?;

        // Send HTTP GET request
        let request = format!(
            "GET {} HTTP/1.1\r\nHost: {}\r\nConnection: close\r\n\r\n",
            path, host
        );

        stream.write_all(request.as_bytes()).await
            .map_err(|e| anyhow!("Failed to send HTTP request: {}", e))?;

        // Read response
        let mut response = String::new();
        stream.read_to_string(&mut response).await
            .map_err(|e| anyhow!("Failed to read HTTP response: {}", e))?;

        debug!("HTTP response received ({} bytes)", response.len());
        Ok(response)
    }

    /// Test HTTP connectivity through proxy
    pub async fn test_http_connectivity(&self) -> Result<bool> {
        info!("Testing HTTP connectivity through SOCKS5 proxy");

        match self.send_http_request("httpbin.org", "/ip").await {
            Ok(response) => {
                if response.contains("200 OK") {
                    info!("HTTP connectivity test successful");
                    debug!("Response preview: {}", 
                           response.lines().take(5).collect::<Vec<_>>().join("\n"));
                    Ok(true)
                } else {
                    warn!("HTTP connectivity test failed: unexpected response");
                    Ok(false)
                }
            }
            Err(e) => {
                error!("HTTP connectivity test failed: {}", e);
                Ok(false)
            }
        }
    }

    /// Get proxy address
    pub fn get_proxy_addr(&self) -> &str {
        &self.proxy_addr
    }

    /// Check if authentication is configured
    pub fn has_authentication(&self) -> bool {
        self.username.is_some() && self.password.is_some()
    }
}

/// SOCKS5 connection pool for managing multiple connections
pub struct Socks5ConnectionPool {
    clients: std::collections::HashMap<String, Socks5Client>,
}

impl Socks5ConnectionPool {
    pub fn new() -> Self {
        Self {
            clients: std::collections::HashMap::new(),
        }
    }

    /// Add SOCKS5 client to pool
    pub fn add_client(&mut self, name: String, client: Socks5Client) {
        self.clients.insert(name, client);
    }

    /// Get client by name
    pub fn get_client(&self, name: &str) -> Option<&Socks5Client> {
        self.clients.get(name)
    }

    /// Remove client from pool
    pub fn remove_client(&mut self, name: &str) -> Option<Socks5Client> {
        self.clients.remove(name)
    }

    /// Test all clients in pool
    pub async fn test_all_clients(&self) -> std::collections::HashMap<String, bool> {
        let mut results = std::collections::HashMap::new();
        
        for (name, client) in &self.clients {
            let is_working = client.test_http_connectivity().await.unwrap_or(false);
            results.insert(name.clone(), is_working);
        }
        
        results
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_socks5_client_creation() {
        // This test requires the actual SOCKS5 server to be running
        // Skip in CI/CD environments
        if std::env::var("CI").is_ok() {
            return;
        }

        let result = Socks5Client::new(
            "**************:8888",
            Some("lunyduo".to_string()),
            Some("lunyduo888".to_string()),
        ).await;

        match result {
            Ok(client) => {
                assert!(client.has_authentication());
                assert_eq!(client.get_proxy_addr(), "**************:8888");
            }
            Err(e) => {
                // Connection might fail in test environment
                println!("SOCKS5 test skipped due to connection error: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_connection_pool() {
        let mut pool = Socks5ConnectionPool::new();
        
        // Test empty pool
        assert!(pool.get_client("test").is_none());
        
        // Pool operations don't require actual connections
        assert_eq!(pool.clients.len(), 0);
    }
}
