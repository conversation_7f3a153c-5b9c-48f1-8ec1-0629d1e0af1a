use anyhow::{Result, anyhow};
use std::net::IpAddr;
use std::process::Command;
use async_trait::async_trait;
use log::{info, warn, error, debug};

use crate::traits::RouteManager;

/// Cross-platform route manager
pub struct SystemRouteManager {
    original_gateway: Option<IpAddr>,
    added_routes: Vec<String>,
}

impl SystemRouteManager {
    pub fn new() -> Self {
        Self {
            original_gateway: None,
            added_routes: Vec::new(),
        }
    }

    /// Execute system command
    fn execute_command(&self, command: &str, args: &[&str]) -> Result<String> {
        debug!("Executing command: {} {}", command, args.join(" "));
        
        let output = Command::new(command)
            .args(args)
            .output()
            .map_err(|e| anyhow!("Failed to execute command: {}", e))?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow!("Command failed: {}", stderr));
        }

        let stdout = String::from_utf8_lossy(&output.stdout);
        Ok(stdout.to_string())
    }

    /// Get default gateway on macOS/Linux
    #[cfg(unix)]
    fn get_default_gateway_unix(&self) -> Result<IpAddr> {
        #[cfg(target_os = "macos")]
        {
            let output = self.execute_command("route", &["-n", "get", "default"])?;
            for line in output.lines() {
                if line.trim().starts_with("gateway:") {
                    let gateway_str = line.split(':').nth(1)
                        .ok_or_else(|| anyhow!("Invalid gateway line"))?
                        .trim();
                    return gateway_str.parse()
                        .map_err(|e| anyhow!("Invalid gateway IP: {}", e));
                }
            }
        }

        #[cfg(target_os = "linux")]
        {
            let output = self.execute_command("ip", &["route", "show", "default"])?;
            for line in output.lines() {
                if line.contains("default via") {
                    let parts: Vec<&str> = line.split_whitespace().collect();
                    if parts.len() >= 3 && parts[0] == "default" && parts[1] == "via" {
                        return parts[2].parse()
                            .map_err(|e| anyhow!("Invalid gateway IP: {}", e));
                    }
                }
            }
        }

        Err(anyhow!("Could not find default gateway"))
    }

    /// Get default gateway on Windows
    #[cfg(windows)]
    fn get_default_gateway_windows(&self) -> Result<IpAddr> {
        let output = self.execute_command("route", &["print", "0.0.0.0"])?;
        
        for line in output.lines() {
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 3 && parts[0] == "0.0.0.0" && parts[1] == "0.0.0.0" {
                return parts[2].parse()
                    .map_err(|e| anyhow!("Invalid gateway IP: {}", e));
            }
        }

        Err(anyhow!("Could not find default gateway"))
    }

    /// Add route on macOS/Linux
    #[cfg(unix)]
    fn add_route_unix(&self, destination: &str, gateway: IpAddr) -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            self.execute_command("route", &[
                "add", "-net", destination, gateway.to_string().as_str()
            ])?;
        }

        #[cfg(target_os = "linux")]
        {
            self.execute_command("ip", &[
                "route", "add", destination, "via", &gateway.to_string()
            ])?;
        }

        Ok(())
    }

    /// Add route on Windows
    #[cfg(windows)]
    fn add_route_windows(&self, destination: &str, gateway: IpAddr) -> Result<()> {
        self.execute_command("route", &[
            "add", destination, gateway.to_string().as_str()
        ])?;
        Ok(())
    }

    /// Remove route on macOS/Linux
    #[cfg(unix)]
    fn remove_route_unix(&self, destination: &str) -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            self.execute_command("route", &["delete", "-net", destination])?;
        }

        #[cfg(target_os = "linux")]
        {
            self.execute_command("ip", &["route", "del", destination])?;
        }

        Ok(())
    }

    /// Remove route on Windows
    #[cfg(windows)]
    fn remove_route_windows(&self, destination: &str) -> Result<()> {
        self.execute_command("route", &["delete", destination])?;
        Ok(())
    }

    /// Set default route on macOS/Linux
    #[cfg(unix)]
    fn set_default_route_unix(&self, tun_ip: IpAddr) -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            self.execute_command("route", &[
                "add", "-net", "0.0.0.0/1", tun_ip.to_string().as_str()
            ])?;
            self.execute_command("route", &[
                "add", "-net", "*********/1", tun_ip.to_string().as_str()
            ])?;
        }

        #[cfg(target_os = "linux")]
        {
            self.execute_command("ip", &[
                "route", "add", "0.0.0.0/1", "via", &tun_ip.to_string()
            ])?;
            self.execute_command("ip", &[
                "route", "add", "*********/1", "via", &tun_ip.to_string()
            ])?;
        }

        Ok(())
    }

    /// Set default route on Windows
    #[cfg(windows)]
    fn set_default_route_windows(&self, tun_ip: IpAddr) -> Result<()> {
        self.execute_command("route", &[
            "add", "0.0.0.0", "mask", "*********", &tun_ip.to_string()
        ])?;
        self.execute_command("route", &[
            "add", "*********", "mask", "*********", &tun_ip.to_string()
        ])?;
        Ok(())
    }

    /// Restore default route on macOS/Linux
    #[cfg(unix)]
    fn restore_default_route_unix(&self) -> Result<()> {
        #[cfg(target_os = "macos")]
        {
            let _ = self.execute_command("route", &["delete", "-net", "0.0.0.0/1"]);
            let _ = self.execute_command("route", &["delete", "-net", "*********/1"]);
        }

        #[cfg(target_os = "linux")]
        {
            let _ = self.execute_command("ip", &["route", "del", "0.0.0.0/1"]);
            let _ = self.execute_command("ip", &["route", "del", "*********/1"]);
        }

        Ok(())
    }

    /// Restore default route on Windows
    #[cfg(windows)]
    fn restore_default_route_windows(&self) -> Result<()> {
        let _ = self.execute_command("route", &["delete", "0.0.0.0", "mask", "*********"]);
        let _ = self.execute_command("route", &["delete", "*********", "mask", "*********"]);
        Ok(())
    }
}

#[async_trait]
impl RouteManager for SystemRouteManager {
    /// Add system route to direct traffic through TUN interface
    async fn add_route(&self, destination: &str, gateway: IpAddr) -> Result<()> {
        info!("Adding route: {} via {}", destination, gateway);

        #[cfg(unix)]
        self.add_route_unix(destination, gateway)?;

        #[cfg(windows)]
        self.add_route_windows(destination, gateway)?;

        debug!("Route added successfully");
        Ok(())
    }

    /// Remove system route
    async fn remove_route(&self, destination: &str) -> Result<()> {
        info!("Removing route: {}", destination);

        #[cfg(unix)]
        self.remove_route_unix(destination)?;

        #[cfg(windows)]
        self.remove_route_windows(destination)?;

        debug!("Route removed successfully");
        Ok(())
    }

    /// Get default gateway
    async fn get_default_gateway(&self) -> Result<IpAddr> {
        debug!("Getting default gateway");

        #[cfg(unix)]
        let gateway = self.get_default_gateway_unix()?;

        #[cfg(windows)]
        let gateway = self.get_default_gateway_windows()?;

        info!("Default gateway: {}", gateway);
        Ok(gateway)
    }

    /// Set default route through TUN interface
    async fn set_default_route(&self, tun_ip: IpAddr) -> Result<()> {
        info!("Setting default route through TUN interface: {}", tun_ip);

        // Save original gateway
        if self.original_gateway.is_none() {
            let _gateway = self.get_default_gateway().await?;
            // Note: This is not thread-safe, but for simplicity we'll keep it
            // In a real implementation, you'd use proper synchronization
        }

        #[cfg(unix)]
        self.set_default_route_unix(tun_ip)?;

        #[cfg(windows)]
        self.set_default_route_windows(tun_ip)?;

        info!("Default route set successfully");
        Ok(())
    }

    /// Restore original default route
    async fn restore_default_route(&self) -> Result<()> {
        info!("Restoring original default route");

        #[cfg(unix)]
        self.restore_default_route_unix()?;

        #[cfg(windows)]
        self.restore_default_route_windows()?;

        info!("Original default route restored");
        Ok(())
    }

    /// Check if we have admin privileges for route modification
    fn has_admin_privileges(&self) -> bool {
        #[cfg(unix)]
        {
            unsafe { libc::geteuid() == 0 }
        }

        #[cfg(windows)]
        {
            // On Windows, check if we can write to system directories
            std::fs::metadata("C:\\Windows\\System32").is_ok()
        }
    }

    /// Request admin privileges if needed
    async fn request_admin_privileges(&self) -> Result<()> {
        if self.has_admin_privileges() {
            return Ok(());
        }

        info!("Requesting admin privileges...");

        #[cfg(unix)]
        {
            return Err(anyhow!("Admin privileges required. Please run with sudo."));
        }

        #[cfg(windows)]
        {
            // On Windows, we would need to restart the application with elevated privileges
            return Err(anyhow!("Admin privileges required. Please run as administrator."));
        }
    }
}

/// Check if admin privileges are available
pub fn check_admin_privileges() -> bool {
    let route_manager = SystemRouteManager::new();
    route_manager.has_admin_privileges()
}

/// Request admin privileges using the privilege crate
pub fn request_privileges() -> Result<()> {
    info!("Checking privilege requirements...");

    // Simplified privilege check for now
    #[cfg(unix)]
    {
        if unsafe { libc::geteuid() } != 0 {
            return Err(anyhow!("Admin privileges required. Please run with sudo."));
        }
    }

    #[cfg(windows)]
    {
        // On Windows, assume we have privileges if we can access system directories
        if std::fs::metadata("C:\\Windows\\System32").is_err() {
            return Err(anyhow!("Admin privileges required. Please run as administrator."));
        }
    }

    info!("Running with sufficient privileges");
    Ok(())
}
