/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/tun-proxy-ui: /Users/<USER>/code/github/tun_proxy_ui/src-tauri/build.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/capabilities /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/config.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/dns.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/lib.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/main.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/packet.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/proxy.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/route.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/rules.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/socks5.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/test_backend.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/traits.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/src/tun.rs /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/45f1706e214e051e2ea25d5fcefb1d3a2ca8c5cc7ea0df00a57ce212cf0da8c5 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/a6bae871e3b336e8befd9c050cca8ed92de3c858f0b909158188b10d278aec65 /Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tun-proxy-ui-1ea96ac6df498065/out/c00117533225ca5a959bd1c8d3760e285f6caefb76214c5f7263638d2ddca70c /Users/<USER>/code/github/tun_proxy_ui/src-tauri/tauri.conf.json
