{"rustc": 12610991425282158916, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 8276155916380437441, "path": 12738171161949863586, "deps": [[2671782512663819132, "tauri_utils", false, 485403277451513585], [3150220818285335163, "url", false, 2297118740214412204], [4143744114649553716, "raw_window_handle", false, 12995286393908122963], [6089812615193535349, "build_script_build", false, 17170841873206453901], [7606335748176206944, "dpi", false, 1045237740745303680], [9010263965687315507, "http", false, 7324982354021803686], [9689903380558560274, "serde", false, 3511246466896572766], [10806645703491011684, "thiserror", false, 9850513865862821905], [15367738274754116744, "serde_json", false, 13021332167267879974], [16727543399706004146, "cookie", false, 2227274486810096283]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-feb0cb86a570a911/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}