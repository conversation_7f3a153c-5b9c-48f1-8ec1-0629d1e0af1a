{"rustc": 12610991425282158916, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 2492918527959507105], [8324462083842905811, "build_script_build", false, 1248026697900824877], [10075813589556262423, "build_script_build", false, 13549336989627928672]], "local": [{"RerunIfChanged": {"output": "debug/build/tun-proxy-ui-7e5a501cdcc1a714/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}