{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 8276155916380437441, "path": 12460502003051922982, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 7102418381076012000], [3150220818285335163, "url", false, 2297118740214412204], [3191507132440681679, "serde_untagged", false, 6909019928609114711], [4071963112282141418, "serde_with", false, 973060535362245011], [4899080583175475170, "semver", false, 5831343530093039187], [5986029879202738730, "log", false, 2949314430116619582], [6606131838865521726, "ctor", false, 6702809386760566390], [7170110829644101142, "json_patch", false, 18073021967206899305], [8319709847752024821, "uuid", false, 14174480796910639246], [9010263965687315507, "http", false, 7324982354021803686], [9451456094439810778, "regex", false, 9492573904540591588], [9556762810601084293, "brotli", false, 9285500792737329297], [9689903380558560274, "serde", false, 3511246466896572766], [10806645703491011684, "thiserror", false, 9850513865862821905], [11989259058781683633, "dunce", false, 3731510677783742233], [13625485746686963219, "anyhow", false, 16040947391945431674], [15367738274754116744, "serde_json", false, 13021332167267879974], [15609422047640926750, "toml", false, 14007308174788123276], [15622660310229662834, "walkdir", false, 7577686223010929625], [15932120279885307830, "memchr", false, 11317857473927389336], [17146114186171651583, "infer", false, 13033184025763804814], [17155886227862585100, "glob", false, 8580673894311488543], [17186037756130803222, "phf", false, 9306028868477560007]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-utils-f7709f8fcf85d5a9/dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}