{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"macos-private-api\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 8276155916380437441, "path": 18184000869863331234, "deps": [[1386409696764982933, "objc2", false, 6065389473597798076], [2671782512663819132, "tauri_utils", false, 485403277451513585], [3150220818285335163, "url", false, 2297118740214412204], [4143744114649553716, "raw_window_handle", false, 12995286393908122963], [5986029879202738730, "log", false, 2949314430116619582], [6089812615193535349, "tauri_runtime", false, 12806873517612938933], [8826339825490770380, "tao", false, 11064659262977521170], [9010263965687315507, "http", false, 7324982354021803686], [9141053277961803901, "wry", false, 14650375151458680559], [9859211262912517217, "objc2_foundation", false, 18166526998043396719], [10575598148575346675, "objc2_app_kit", false, 17744769613071665242], [11599800339996261026, "build_script_build", false, 16367373529599254031]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-e228ca5337ba1b85/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}