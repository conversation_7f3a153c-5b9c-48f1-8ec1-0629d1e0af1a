{"rustc": 12610991425282158916, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 3033921117576893, "path": 13554723975383068289, "deps": [[2616743************, "plist", false, 11991966871634880968], [2671782512663819132, "tauri_utils", false, 4906042191349930192], [3060637413840920116, "proc_macro2", false, 16533576608068259436], [3150220818285335163, "url", false, 17719527386040044314], [4899080583175475170, "semver", false, 6282566034919374745], [4974441333307933176, "syn", false, 2247998595589678503], [7170110829644101142, "json_patch", false, 17945681723495854454], [7392050791754369441, "ico", false, 1729724600918168840], [8319709847752024821, "uuid", false, 16172666988391654141], [9556762810601084293, "brotli", false, 861412817568862249], [9689903380558560274, "serde", false, 745942480493426984], [9857275760291862238, "sha2", false, 9889837466140325404], [10806645703491011684, "thiserror", false, 9812569775333717302], [12409575957772518135, "time", false, 2147696019685299773], [12687914511023397207, "png", false, 14869814351652569571], [13077212702700853852, "base64", false, 7739903852715691330], [15367738274754116744, "serde_json", false, 15388988034957338642], [15622660310229662834, "walkdir", false, 6608244980976215650], [17990358020177143287, "quote", false, 14352002541199012783]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-codegen-86fcc3a84877c5ea/dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}