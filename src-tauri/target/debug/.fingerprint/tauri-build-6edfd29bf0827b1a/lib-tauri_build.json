{"rustc": 12610991425282158916, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 3033921117576893, "path": 6981224517068874896, "deps": [[2671782512663819132, "tauri_utils", false, 4906042191349930192], [4899080583175475170, "semver", false, 6282566034919374745], [6913375703034175521, "schemars", false, 15073630213099583027], [7170110829644101142, "json_patch", false, 17945681723495854454], [9689903380558560274, "serde", false, 745942480493426984], [12714016054753183456, "tauri_winres", false, 1909137783861032689], [13077543566650298139, "heck", false, 11883115444871798576], [13475171727366188400, "cargo_toml", false, 2145662322863330552], [13625485746686963219, "anyhow", false, 824702634488630655], [15367738274754116744, "serde_json", false, 15388988034957338642], [15609422047640926750, "toml", false, 13473490468758989476], [15622660310229662834, "walkdir", false, 6608244980976215650], [16928111194414003569, "dirs", false, 8533177109786726849], [17155886227862585100, "glob", false, 7383102877630803948]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-build-6edfd29bf0827b1a/dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}