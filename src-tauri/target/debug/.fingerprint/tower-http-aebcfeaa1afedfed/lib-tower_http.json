{"rustc": 12610991425282158916, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 8276155916380437441, "path": 9504828849963151085, "deps": [[784494742817713399, "tower_service", false, 14016621728702740219], [1906322745568073236, "pin_project_lite", false, 8927401913104314243], [4121350475192885151, "iri_string", false, 10741612714149508030], [5695049318159433696, "tower", false, 10933067625674765957], [7712452662827335977, "tower_layer", false, 1932984889505905327], [7896293946984509699, "bitflags", false, 13694014420434979308], [9010263965687315507, "http", false, 7324982354021803686], [10629569228670356391, "futures_util", false, 4224752671594802412], [14084095096285906100, "http_body", false, 1536282616745696191], [16066129441945555748, "bytes", false, 1820149677739525238]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tower-http-aebcfeaa1afedfed/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}