{"rustc": 12610991425282158916, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"macos-private-api\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 8276155916380437441, "path": 7605895578623567934, "deps": [[40386456601120721, "percent_encoding", false, 15438953231647850255], [1200537532907108615, "url<PERSON><PERSON>n", false, 7102418381076012000], [1386409696764982933, "objc2", false, 6065389473597798076], [2616743947975331138, "plist", false, 11395370076418703099], [2671782512663819132, "tauri_utils", false, 485403277451513585], [3150220818285335163, "url", false, 2297118740214412204], [3331586631144870129, "getrandom", false, 8559765949624896017], [3478659405401458569, "tokio", false, 4270640619702503696], [4143744114649553716, "raw_window_handle", false, 12995286393908122963], [4494683389616423722, "muda", false, 3047550086561100150], [4919829919303820331, "serialize_to_javascript", false, 9621179797751242853], [5986029879202738730, "log", false, 2949314430116619582], [6089812615193535349, "tauri_runtime", false, 12806873517612938933], [7573826311589115053, "tauri_macros", false, 6075653625979512320], [8589231650440095114, "embed_plist", false, 5457514598030221730], [9010263965687315507, "http", false, 7324982354021803686], [9689903380558560274, "serde", false, 3511246466896572766], [9859211262912517217, "objc2_foundation", false, 18166526998043396719], [10229185211513642314, "mime", false, 1375056873091504961], [10575598148575346675, "objc2_app_kit", false, 17744769613071665242], [10806645703491011684, "thiserror", false, 9850513865862821905], [11599800339996261026, "tauri_runtime_wry", false, 12362450013462448016], [11989259058781683633, "dunce", false, 3731510677783742233], [12565293087094287914, "window_vibrancy", false, 2179469601665840779], [12986574360607194341, "serde_repr", false, 13314099403699840003], [13077543566650298139, "heck", false, 5384796184464484329], [13625485746686963219, "anyhow", false, 16040947391945431674], [14039947826026167952, "build_script_build", false, 13868316826782695664], [15367738274754116744, "serde_json", false, 13021332167267879974], [16928111194414003569, "dirs", false, 100435355648579084], [17155886227862585100, "glob", false, 8580673894311488543]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-266cd28d8ba3fc8b/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}