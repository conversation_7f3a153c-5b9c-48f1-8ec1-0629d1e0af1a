["/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/get_by_id.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/new.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/remove_by_id.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_icon.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_icon_as_template.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_menu.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_show_menu_on_left_click.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_temp_dir_path.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_title.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_tooltip.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/commands/set_visible.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-e6558b2f7675e25d/out/permissions/tray/autogenerated/default.toml"]