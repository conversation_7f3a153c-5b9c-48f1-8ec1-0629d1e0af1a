["/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/event/autogenerated/commands/emit.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/event/autogenerated/commands/emit_to.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/event/autogenerated/commands/listen.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/event/autogenerated/commands/unlisten.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/event/autogenerated/default.toml"]