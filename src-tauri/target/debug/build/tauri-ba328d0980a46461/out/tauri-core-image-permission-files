["/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/image/autogenerated/commands/from_bytes.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/image/autogenerated/commands/from_path.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/image/autogenerated/commands/new.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/image/autogenerated/commands/rgba.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/image/autogenerated/commands/size.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/image/autogenerated/default.toml"]