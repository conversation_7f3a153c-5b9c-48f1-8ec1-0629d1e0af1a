# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-decorated"
description = "Enables the is_decorated command without any pre-configured scope."
commands.allow = ["is_decorated"]

[[permission]]
identifier = "deny-is-decorated"
description = "Denies the is_decorated command without any pre-configured scope."
commands.deny = ["is_decorated"]
