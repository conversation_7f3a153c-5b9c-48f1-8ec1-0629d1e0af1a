["/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/basename.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/dirname.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/extname.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/is_absolute.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/join.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/normalize.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/resolve.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/commands/resolve_directory.toml", "/Users/<USER>/code/github/tun_proxy_ui/src-tauri/target/debug/build/tauri-ba328d0980a46461/out/permissions/path/autogenerated/default.toml"]