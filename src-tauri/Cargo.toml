[package]
name = "tun-proxy-ui"
version = "0.1.0"
description = "Cross-platform proxy tool with TUN/TAP support"
authors = ["lulin"]
license = "MIT"
repository = ""
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.3.0" }

[dependencies]
# Core Tauri dependencies
tauri = { version = "2.6.2" }
tauri-plugin-log = "2"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
log = "0.4"

# TUN/TAP and networking
tun-easytier = { version = "1.1", features = ["async"] }
tokio = { version = "1.0", features = ["full"] }
tokio-util = "0.7"

# Packet processing
pnet = "0.35"
etherparse = "0.15"

# Encryption and HTTP
aes-gcm = "0.10"
reqwest = { version = "0.12", features = ["json"] }
base64 = "0.22"

# SOCKS5 support
tokio-socks = "0.5"

# Admin privileges
privilege = "0.3"

# DNS and IP utilities
trust-dns-resolver = "0.23"
ipnet = "2.9"
cidr = "0.2"

# Async utilities
futures = "0.3"
async-trait = "0.1"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Configuration
toml = "0.8"

# System utilities
libc = "0.2"

# Platform-specific dependencies
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winnt", "processthreadsapi", "handleapi"] }
windows = { version = "0.58", features = ["Win32_NetworkManagement_IpHelper", "Win32_System_Threading"] }

[target.'cfg(unix)'.dependencies]
nix = "0.29"
